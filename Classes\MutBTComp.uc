class MutBTComp extends Mu<PERSON><PERSON>
	config(BTCompServer);

//	#Mutator
const MOD_VERSION = "3.5";

var string ModName, ModVersion;

var BTRI_Mutator RepInfo;
var BTRI_Warmup WarmupClass;
var BTRI_TimeStamp StampInfo;

//	#ScoreBoard
var config bool bEnableScoreboard;
var config bool bEnableWeaponStats;
var config bool bEnablePowerupStats;

struct ScoreBoardReplace
{
	var class<ScoreBoard> OldSB;
	var class<ScoreBoard> NewSB;
	var class<ScoreBoard> EnhSB;
};
var array<ScoreBoardReplace> ScoreBoardType;

//	#Hud
struct HUDReplace
{
	var class<HudBase> OldHud;
	var class<HudBase> NewHud;
};
var array<HUDReplace> HudType;

//	#Netcode
const AVERDT_SEND_PERIOD = 4.00;

var float AverDT, ClientTimeStamp;
var float LastReplicatedAverDT;

var BT_CollisionCopy PCC;
var BT_FakeProjActor FPM;	// Client-side only

struct WeaponsReplace
{
	var class<Weapon> OldWeapon;
	var class<Weapon> NetWeapon;
	var class<WeaponPickup> OldPickup;
	var class<WeaponPickup> NetPickup;
};
var array<WeaponsReplace> WeaponType;

var bool bDefaultWeaponsChanged;

//
var config bool bEnableVoting;
var config bool bEnableBrightskinsVoting;
var config bool bEnableHitsoundsVoting;
var config bool bEnableWarmupVoting;
var config bool bEnableTeamOverlayVoting;
var config bool bEnableTimedOvertimeVoting;
var config bool bEnableDoubleDamageVoting;
var bool bWarmupDisabled;
var config float VotingPercentRequired;
var config float VotingTimeLimit;

var config bool bEnableDoubleDamage;
var config byte EnableBrightSkinsMode;
var config bool bEnableClanSkins;
var config bool bEnableTeamOverlay;
var config byte EnableHitSoundsMode;
var config bool bEnableWarmup;
var config float WarmupReadyPercentRequired;
var config bool bShowTeamScoresInServerBrowser;

var config byte ServerMaxPlayers;
var config bool bEnableAdvancedVotingOptions;

var config bool bEnableAutoDemoRec;
var config string AutoDemoRecMask;
var config byte EnableWarmupWeaponsMode;
var config int WarmupTime;
var config int WarmupHealth;

var config bool bEnableTimedOvertime;
var config int TimedOverTimeLength;
var config int NumGrenadesOnSpawn;

var bool bDemoStarted;

var config bool bShieldFix;
var config bool bAllowRestartVoteEvenIfMapVotingIsTurnedOff;

var config int MaxMultiDodges;

var config int MinNetSpeed;
var config int MaxNetSpeed;

/* ----Known issues----
   Mutant: No Bskins/Forcemodel
   Invasion: No Bskins/forcemodel on bots (but will on players), no warmup, no custom scoreboard
   Assault: No Warmup (uses assaults native warmup)
   LMS: No custom scoreboard (original 1 to extend real buggy)
-------------------------- */

var UTComp_OverlayUpdate OverlayClass;
var UTComp_VotingHandler VotingClass;
var bool bHasInteraction;

var string OrigController;
var class<PlayerController> OrigCClass;

var float StampArray[256];
var float Counter;
var Controller CounterController;
var Pawn CounterPawn;

//==========================
// Begin Enhanced Netcode stuff
//==========================

var bool bEnhancedNetCodeEnabledAtStartOfMap;

var config bool bEnableEnhancedNetCode;
var config bool bEnableEnhancedNetCodeVoting;
//==========================
// End Enhanced Netcode stuff
//==========================

static final function MutBTComp Instance(LevelInfo Level)
{
	local Mutator Mut;

	if (Level.Game != none)
	{
		for (Mut = Level.Game.BaseMutator; Mut != none; Mut = Mut.NextMutator)
		{
			if (MutBTComp(Mut) != none)
				return MutBTComp(Mut);
		}
	}

	return none;
}

function PreBeginPlay()
{
	local int x;

	// #ModName
	x = InStr(string(default.Class), ".");
	default.ModName = Left(string(default.Class), x + 1);	// example: "MutBTComp."
	ModName = default.ModName;

	// #ModVersion
	default.ModVersion = MOD_VERSION;
	ModVersion = default.ModVersion;

	// #Pawn Replace
	if (Level.Game.DefaultPlayerClassName ~= "xGame.xPawn")
		Level.Game.DefaultPlayerClassName = string(class'BS_xPawn');

	if (class'xPawn'.default.ControllerClass == class'xGame.xBot')
		class'xPawn'.default.ControllerClass = class'BS_xBot';

	Level.Game.PlayerControllerClassName = string(class'BS_xPlayer');

	SetupDD();
	SetupStats();
	SetupVoting();
	SetupColoredDeathMessages();

	StaticSaveConfig();
	bEnhancedNetCodeEnabledAtStartOfMap = bEnableEnhancedNetCode;

	Super.PreBeginPlay();
}

function SetupDD()
{
	// Empty function
}

function SetupColoredDeathMessages()
{
	if (Level.Game.DeathMessageClass == class'xGame.xDeathMessage')
		Level.Game.DeathMessageClass = class'BTM_xDeathMessage';
	else if (Level.Game.DeathMessageClass == class'SkaarjPack.InvasionDeathMessage')
		Level.Game.DeathMessageClass = class'BTM_InvasionDeathMessage';
}

function ModifyPlayer(Pawn Other)
{
	local Inventory Inv;
	local int i;

	if (WarmupClass != none && (WarmupClass.bInWarmup == true || WarmupClass.bGivePlayerWeaponHack))
	{
		switch (EnableWarmupWeaponsMode)
		{
			case 0:
				break;

			case 3:
				Other.CreateInventory("Onslaught.ONSGrenadeLauncher");
				Other.CreateInventory("Onslaught.ONSAVRiL");
				Other.CreateInventory("Onslaught.ONSMineLayer");

			case 2:
				Other.CreateInventory("XWeapons.SniperRifle");
				Other.CreateInventory("XWeapons.RocketLauncher");
				Other.CreateInventory("XWeapons.FlakCannon");
				Other.CreateInventory("XWeapons.Minigun");
				Other.CreateInventory("XWeapons.LinkGun");
				Other.CreateInventory("XWeapons.ShockRifle");
				Other.CreateInventory("XWeapons.BioRifle");
				Other.CreateInventory("XWeapons.AssaultRifle");
				Other.CreateInventory("XWeapons.ShieldGun");
				break;

			case 1:
				if (!WarmupClass.bWeaponsChecked)
					WarmupClass.FindWhatWeaponsToGive();
				for (i = 0; i < WarmupClass.sWeaponsToGive.Length; i++)
					Other.CreateInventory(WarmupClass.sWeaponsToGive[i]);
		}

		for (Inv = Other.Inventory; Inv != none; Inv = Inv.Inventory)
		{
			if (Weapon(Inv) != none)
			{
				Weapon(Inv).SuperMaxOutAmmo();
				Weapon(Inv).Loaded();
			}
		}

		if (WarmupHealth != 0)
			Other.Health = WarmupHealth;
		else
			Other.Health = 199;
	}

	if (bEnhancedNetCodeEnabledAtStartOfMap)
	{
		SpawnCollisionCopy(Other);
		if (PCC != none)
			PCC = PCC.RemoveOldPawns();
	}

	if (BS_xPawn(Other) != none)
		BS_xPawn(Other).MultiDodgesRemaining = RepInfo.MaxMultiDodges;

	Super.ModifyPlayer(Other);
}

function DriverEnteredVehicle(Vehicle V, Pawn P)
{
	SpawnCollisionCopy(V);

	if (NextMutator != none)
		NextMutator.DriverEnteredVehicle(V, P);
}

function SpawnCollisionCopy(Pawn Other)
{
	if (PCC == none)
	{
		PCC = Spawn(class'BT_CollisionCopy');
		PCC.SetPawn(Other);
	}
	else
	{
		PCC.AddPawnToList(Other);
	}
}

function ListPawns()
{
	local BT_CollisionCopy PCC_Iterator;

	for (PCC_Iterator = PCC; PCC_Iterator != none; PCC_Iterator = PCC_Iterator.Next)
		PCC_Iterator.Identify();
}

static function bool IsPredicted(Actor A)
{
	if (A == none || xPawn(A) != none || Monster(A) != none)
		return true;

	if ((Vehicle(A) != none && Vehicle(A).Driver != none))
		return true;

	return false;
}

function SetupTeamOverlay()
{
	if (!bEnableTeamOverlay || !Level.Game.bTeamGame)
		return;

	if (OverlayClass == none)
		OverlayClass = Spawn(class'UTComp_OverlayUpdate', self);

	if (OverlayClass != none)
	{
		OverlayClass.UTCompMutator = self;
		OverlayClass.InitializeOverlay();
	}
}

function SetupWarmup()
{
	if (!bEnableWarmup || Level.Game.IsA('ASGameInfo') || Level.Game.IsA('Invasion') || Level.Title ~= "Bollwerk Ruins 2004 - Pro Edition")
	{
		bWarmupDisabled = true;
		return;
	}

	if (WarmupClass == none)
		WarmupClass = Spawn(class'BTRI_Warmup', self);

	if (WarmupClass != none)
	{
		WarmupClass.iWarmupTime = WarmupTime;
		WarmupClass.fReadyPercent = WarmupReadyPercentRequired;
		WarmupClass.InitializeWarmup();
	}
}

function SetupVoting()
{
	if (!bEnableVoting)
		return;

	if (VotingClass == none)
		VotingClass = Spawn(class'UTComp_VotingHandler', self);

	if (VotingClass != none)
	{
		VotingClass.fVotingTime = VotingTimeLimit;
		VotingClass.fVotingPercent = VotingPercentRequired;
		VotingClass.UTCompMutator = self;
		VotingClass.InitializeVoting();
	}
}

function SetupStats()
{
	class'xWeapons.TransRecall'.default.Transmaterials[0] = none;
	class'xWeapons.TransRecall'.default.Transmaterials[1] = none;

	if (!bEnableWeaponStats)
		return;

	if (bEnableEnhancedNetcode)
		class'xWeapons.ShieldFire'.default.AutoFireTestFreq = 0.050000;

	class'xWeapons.AssaultRifle'.default.FireModeClass[0] = class'NewNet_AssaultFire';
	class'xWeapons.AssaultRifle'.default.FireModeClass[1] = class'NewNet_AssaultGrenade';

	class'xWeapons.BioRifle'.default.FireModeClass[0] = class'NewNet_BioFire';
	class'xWeapons.BioRifle'.default.FireModeClass[1] = class'NewNet_BioChargedFire';

	class'xWeapons.ShockRifle'.default.FireModeClass[0] = class'NewNet_ShockBeamFire';
	class'xWeapons.ShockRifle'.default.FireModeClass[1] = class'NewNet_ShockProjFire';

	class'xWeapons.LinkGun'.default.FireModeClass[0] = class'NewNet_LinkAltFire';
	class'xWeapons.LinkGun'.default.FireModeClass[1] = class'NewNet_LinkFire';

	class'xWeapons.Minigun'.default.FireModeClass[0] = class'NewNet_MinigunFire';
	class'xWeapons.Minigun'.default.FireModeClass[1] = class'NewNet_MinigunAltFire';

	class'xWeapons.FlakCannon'.default.FireModeClass[0] = class'NewNet_FlakFire';
	class'xWeapons.FlakCannon'.default.FireModeClass[1] = class'NewNet_FlakAltFire';

	class'xWeapons.RocketLauncher'.default.FireModeClass[0] = class'NewNet_RocketFire';
	class'xWeapons.RocketLauncher'.default.FireModeClass[1] = class'NewNet_RocketAltFire';

	class'xWeapons.SniperRifle'.default.FireModeClass[0] = class'NewNet_LightningFire';
	class'UTClassic.ClassicSniperRifle'.default.FireModeClass[0] = class'NewNet_ClassicSniperFire';

	class'Onslaught.ONSMineLayer'.default.FireModeClass[0] = class'NewNet_ONSMineThrowFire';
	class'Onslaught.ONSGrenadeLauncher'.default.FireModeClass[0] = class'NewNet_ONSGrenadeFire';
	class'OnsLaught.ONSAvril'.default.FireModeClass[0] = class'NewNet_ONSAvrilFire';

	class'xWeapons.SuperShockRifle'.default.FireModeClass[0] = class'NewNet_SuperShockBeamFire';
	class'xWeapons.SuperShockRifle'.default.FireModeClass[1] = class'NewNet_SuperShockBeamFire';
}

simulated function Tick(float DeltaTime)
{
	local PlayerController PC;
	local Mutator M;
	local int x;

	if (Level.NetMode == NM_DedicatedServer)
	{
		if (bEnhancedNetCodeEnabledAtStartOfMap)
		{
			if (!bDefaultWeaponsChanged)
			{
				bDefaultWeaponsChanged = true;
				// replace DefaultWeaponName (fix for simple Arena mutators)
				for (M = Level.Game.BaseMutator; M != none; M = M.NextMutator)
				{
					if (M.DefaultWeaponName != "")
					{
						for (x = 0; x < WeaponType.Length; x++)
						{
							if (M.DefaultWeaponName ~= string(WeaponType[x].OldWeapon))
							{
								M.DefaultWeaponName = string(WeaponType[x].NetWeapon);
								break;
							}
						}
					}
				}
			}

			ClientTimeStamp += DeltaTime;
			Counter += 1;
			StampArray[byte(Counter)] = ClientTimeStamp;	//Counter % 256
			AverDT = (9.0 * AverDT + DeltaTime) * 0.1;

			SetPawnStamp();

			if (ClientTimeStamp > LastReplicatedAverDT + AVERDT_SEND_PERIOD)
			{
				if (StampInfo != none)
					StampInfo.ReplicatedAverDT(AverDT);
				LastReplicatedAverDT = ClientTimeStamp;
			}
		}

		if (!bEnableAutoDemoRec || bDemoStarted || (default.bEnableWarmup && !bWarmupDisabled) || Level.Game.bWaitingToStartMatch)
			return;
		else
			AutoDemoRecord();
		return;
	}

	// Client-side logic
	if (Level.NetMode == NM_Client)
	{
		if (FPM == none)
			FPM = Spawn(class'BT_FakeProjActor');

		if (bHasInteraction)
			return;

		PC = Level.GetLocalPlayerController();
		if (PC != none && PC.Player != none && PC.Player.InteractionMaster != none)
		{
			PC.Player.InteractionMaster.AddInteraction(string(class'BT_Interaction'), PC.Player);
			bHasInteraction = true;
			class'DamTypeLinkShaft'.default.bSkeletize = false;
		}
	}
}

function SetPawnStamp()
{
	local Rotator R;
	local int i;

	if (CounterPawn == none)
	{
		if (CounterController == none)
			CounterController = Spawn(class'BT_StampController');

		if (CounterController != none && CounterController.Pawn != none)
			CounterPawn = CounterController.Pawn;

		if (CounterPawn == none)
			return;
	}

	R.Yaw = (byte(Counter)) * 256;	//Counter % 256
	i = Counter / 256;
	R.Pitch = i * 256;

	CounterPawn.SetRotation(R);
}

simulated function float GetStamp(int Stamp)
{
	return StampArray[byte(Stamp)];	//Stamp % 256
}

function SpawnReplicationClass()
{
	if (RepInfo == none)
		RepInfo = Spawn(class'BTRI_Mutator', self);

	if (RepInfo == none) // Guard against spawn failure
	{
		Warn("MutBTComp: Failed to spawn BTRI_Mutator replication info");
		return;
	}

	RepInfo.bEnableScoreboard = bEnableScoreboard;
	RepInfo.bEnableWeaponStats = bEnableWeaponStats;
	RepInfo.bEnablePowerupStats = bEnablePowerupStats;

	RepInfo.bEnableVoting = bEnableVoting;
	RepInfo.EnableBrightSkinsMode = Clamp(EnableBrightSkinsMode, 1, 3);
	RepInfo.bEnableClanSkins = bEnableClanSkins;
	RepInfo.bEnableTeamOverlay = bEnableTeamOverlay;
	RepInfo.EnableHitSoundsMode = EnableHitSoundsMode;
	RepInfo.bEnableWarmup = bEnableWarmup;
	RepInfo.bEnableBrightskinsVoting = bEnableBrightskinsVoting;
	RepInfo.bEnableHitsoundsVoting = bEnableHitsoundsVoting;
	RepInfo.bEnableWarmupVoting = bEnableWarmupVoting;
	RepInfo.bEnableTeamOverlayVoting = bEnableTeamOverlayVoting;
	RepInfo.ServerMaxPlayers = ServerMaxPlayers;
	RepInfo.bEnableDoubleDamage = bEnableDoubleDamage;
	RepInfo.bEnableDoubleDamageVoting = bEnableDoubleDamageVoting;

	RepInfo.bEnableAdvancedVotingOptions = bEnableAdvancedVotingOptions;
	RepInfo.bEnableTimedOvertimeVoting = bEnableTimedOvertimeVoting;
	RepInfo.bEnableTimedOvertime = bEnableTimedOvertime;
	RepInfo.bEnableEnhancedNetcode = bEnableEnhancedNetcode;
	RepInfo.bEnableEnhancedNetcodeVoting = bEnableEnhancedNetcodeVoting;
	RepInfo.MaxMultiDodges = MaxMultiDodges;
	RepInfo.MinNetSpeed = MinNetSpeed;
	RepInfo.MaxNetSpeed = MaxNetSpeed;
	RepInfo.bShieldFix = bShieldFix;
	RepInfo.bAllowRestartVoteEvenIfMapVotingIsTurnedOff = bAllowRestartVoteEvenIfMapVotingIsTurnedOff;

	if (
		Level.Game.IsA('CTFGame') || Level.Game.IsA('ONSONslaughtGame') || Level.Game.IsA('ASGameInfo') ||
		Level.Game.IsA('xBombingRun') || Level.Game.IsA('xMutantGame') || Level.Game.IsA('xLastManStandingGame') ||
		Level.Game.IsA('xDoubleDom') || Level.Game.IsA('Invasion')
	)
	{
		RepInfo.bEnableTimedOvertime = false;
		bEnableTimedOvertime = false;
	}
}

function PostBeginPlay()
{
	local BT_GameRules G;
	local Mutator M;
	local string URLQuery;

	Super.PostBeginPlay();

	URLQuery = Level.GetLocalURL();
	URLQuery = Mid(URLQuery, InStr(URLQuery, "?"));
	if (URLQuery != "")
		ParseURL(URLQuery);

	SetupTeamOverlay();
	SetupWarmup();
	SpawnReplicationClass();

	G = Spawn(class'BT_GameRules');
	if (G != none)
	{
		G.UTCompMutator = self;
		G.OVERTIMETIME = TimedOverTimeLength;

		if (Level.Game.GameRulesModifiers == none)
			Level.Game.GameRulesModifiers = G;
		else
			Level.Game.GameRulesModifiers.AddGameRules(G);
	}

	if (StampInfo == none && bEnhancedNetCodeEnabledAtStartOfMap)
		StampInfo = Spawn(class'BTRI_TimeStamp');

	for (M = Level.Game.BaseMutator; M != none; M = M.NextMutator)
	{
		if (string(M.Class) ~= "SpawnGrenades.MutSN")
			return;
	}

	class'GrenadeAmmo'.default.InitialAmount = NumGrenadesOnSpawn;
}

function bool CheckReplacement(Actor Other, out byte bSuperRelevant)
{
	local int i, weapIdx;
	local WeaponLocker L;
	local LinkedReplicationInfo LPRI;

	bSuperRelevant = 0;
	if (Other == none)
		return true;

	if (bEnhancedNetCodeEnabledAtStartOfMap)
	{
		// # Replace WeaponBase, WeaponPickup, WeaponLocker, Monster PCC
		if (xWeaponBase(Other) != none)
		{
			for (i = 0; i < WeaponType.Length; i++)
			{
				if (xWeaponBase(Other).WeaponType == WeaponType[i].OldWeapon)
				{
					xWeaponBase(Other).WeaponType = WeaponType[i].NetWeapon;
					return true;
				}
			}
		}
		else if (WeaponPickup(Other) != none)
		{
			for (i = 0; i < WeaponType.Length; i++)
			{
				if (Other.Class == WeaponType[i].OldPickup && WeaponType[i].NetPickup != none)
				{
					ReplaceWith(Other, string(WeaponType[i].NetPickup));
					return false;
				}
			}
		}
		else if (WeaponLocker(Other) != none)
		{
			L = WeaponLocker(Other);
			for (weapIdx = 0; weapIdx < L.Weapons.Length; weapIdx++)
			{
				for (i = 0; i < WeaponType.Length; i++)
				{
					if (L.Weapons[weapIdx].WeaponClass == WeaponType[i].OldWeapon)
						L.Weapons[weapIdx].WeaponClass = WeaponType[i].NetWeapon;
				}
			}

			return true;
		}
	}

	if (PlayerReplicationInfo(Other) != none)
	{
		LPRI = PlayerReplicationInfo(Other).CustomReplicationInfo;
		if (LPRI == none)
		{
			PlayerReplicationInfo(Other).CustomReplicationInfo = Spawn(class'BTRI_Client', Other.Owner);
			LPRI = PlayerReplicationInfo(Other).CustomReplicationInfo;
		}
		else // Find end of existing LRI chain
		{
			while (LPRI.NextReplicationInfo != none)
			{
				LPRI = LPRI.NextReplicationInfo;
			}

			// LPRI is now the last info in the chain
			LPRI.NextReplicationInfo = Spawn(class'BTRI_Client', Other.Owner);
			LPRI = LPRI.NextReplicationInfo; // Move LPRI to the newly added BTRI_Client
		}

		if (LPRI != none && bEnhancedNetCodeEnabledAtStartOfMap)
			LPRI.NextReplicationInfo = Spawn(class'BTRI_NewNet', Other.Owner);
	}

	if (Other.IsA('UDamagePack') && !GetDoubleDamage())
		return false;

	return true;
}

function bool GetDoubleDamage()
{
	SetupDD();
	return bEnableDoubleDamage;
}

function ModifyLogin(out string Portal, out string Options)
{
	local int i;
	local bool bSeeAll, bSpectator;

	if (Level.Game == none)
	{
		Log("MutBTComp: Level.Game is none in ModifyLogin, aborting.", 'Warning');
		return;
	}

	if (OrigController != "")
	{
		Level.Game.PlayerControllerClassName = OrigController;
		Level.Game.PlayerControllerClass = OrigCClass;
		OrigController = "";
		OrigCClass = none;
	}

	bSpectator = (Level.Game.ParseOption(Options, "SpectatorOnly") ~= "1");
	bSeeAll = (Level.Game.ParseOption(Options, "UTVSeeAll") ~= "true");

	if (bSeeAll && bSpectator)
	{
		Log("MutBTComp: UTV viewer detected, switching to BS_xSpectator controller.");
		OrigController = Level.Game.PlayerControllerClassName;
		OrigCClass = Level.Game.PlayerControllerClass;
		Level.Game.PlayerControllerClassName = string(class'BS_xSpectator');
		Level.Game.PlayerControllerClass = none;
	}

	for (i = 0; i < ScoreBoardType.Length; i++)
	{
		if (Level.Game.ScoreBoardType ~= string(ScoreBoardType[i].OldSB))
		{
			if (bEnableScoreBoard && ScoreBoardType[i].EnhSB != none)
				Level.Game.ScoreBoardType = string(ScoreBoardType[i].EnhSB);
			else if (ScoreBoardType[i].NewSB != none)
				Level.Game.ScoreBoardType = string(ScoreBoardType[i].NewSB);

			if (RepInfo != none)
			{
				if (ScoreBoardType[i].NewSB != none)
					RepInfo.MRI_NormalSB = string(ScoreBoardType[i].NewSB);

				if (ScoreBoardType[i].EnhSB != none)
					RepInfo.MRI_EnhancedSB = string(ScoreBoardType[i].EnhSB);
			}
			break;
		}
	}

	Super.ModifyLogin(Portal, Options);

	for (i = 0; i < HudType.Length; i++)
	{
		if (Level.Game.HudType ~= string(HudType[i].OldHud) && HudType[i].NewHud != none)
		{
			Level.Game.HudType = string(HudType[i].NewHud);
			break;
		}
	}
}

function GetServerPlayers(out GameInfo.ServerResponseLine ServerState)
{
	local int i;

	if (!Level.Game.bTeamGame)
		return;

	if (bShowTeamScoresInServerBrowser && TeamGame(Level.Game).Teams[0] != none)
	{
		i = ServerState.PlayerInfo.Length;
		ServerState.PlayerInfo.Length = i + 1;
		ServerState.PlayerInfo[i].PlayerName = Chr(0x1B) $ Chr(10) $ Chr(245) $ Chr(10) $ "Red Team Score";
		ServerState.PlayerInfo[i].Score = TeamGame(Level.Game).Teams[0].Score;
	}

	if (bShowTeamScoresInServerBrowser && TeamGame(Level.Game).Teams[1] != none)
	{
		i = ServerState.PlayerInfo.Length;
		ServerState.PlayerInfo.Length = i + 1;
		ServerState.PlayerInfo[i].PlayerName = Chr(0x1B) $ Chr(10) $ Chr(245) $ Chr(10) $ "Blue Team Score";
		ServerState.PlayerInfo[i].Score = TeamGame(Level.Game).Teams[1].Score;
	}
}

function ServerTraveling(string URL, bool bItems)
{
	class'xPawn'.default.ControllerClass = class'XGame.XBot';
	class'GrenadeAmmo'.default.InitialAmount = 4;

	class'xWeapons.AssaultRifle'.default.FireModeClass[0] = class'xWeapons.AssaultFire';
	class'xWeapons.AssaultRifle'.default.FireModeClass[1] = class'xWeapons.AssaultGrenade';

	class'xWeapons.BioRifle'.default.FireModeClass[0] = class'xWeapons.BioFire';
	class'xWeapons.BioRifle'.default.FireModeClass[1] = class'xWeapons.BioChargedFire';

	class'xWeapons.ShockRifle'.default.FireModeClass[0] = class'xWeapons.ShockBeamFire';
	class'xWeapons.ShockRifle'.default.FireModeClass[1] = class'xWeapons.ShockProjFire';

	class'xWeapons.LinkGun'.default.FireModeClass[0] = class'xWeapons.LinkAltFire';
	class'xWeapons.LinkGun'.default.FireModeClass[1] = class'xWeapons.LinkFire';

	class'xWeapons.Minigun'.default.FireModeClass[0] = class'xWeapons.MinigunFire';
	class'xWeapons.Minigun'.default.FireModeClass[1] = class'xWeapons.MinigunAltFire';

	class'xWeapons.FlakCannon'.default.FireModeClass[0] = class'xWeapons.FlakFire';
	class'xWeapons.FlakCannon'.default.FireModeClass[1] = class'xWeapons.FlakAltFire';

	class'xWeapons.RocketLauncher'.default.FireModeClass[0] = class'xWeapons.RocketFire';
	class'xWeapons.RocketLauncher'.default.FireModeClass[1] = class'xWeapons.RocketMultiFire';

	class'xWeapons.SniperRifle'.default.FireModeClass[0] = class'xWeapons.SniperFire';
	class'UTClassic.ClassicSniperRifle'.default.FireModeClass[0] = class'UTClassic.ClassicSniperFire';

	class'Onslaught.ONSMineLayer'.default.FireModeClass[0] = class'Onslaught.ONSMineThrowFire';
	class'Onslaught.ONSGrenadeLauncher'.default.FireModeClass[0] = class'Onslaught.ONSGrenadeFire';
	class'Onslaught.ONSAvril'.default.FireModeClass[0] = class'Onslaught.ONSAvrilFire';

	class'xWeapons.SuperShockRifle'.default.FireModeClass[0] = class'xWeapons.SuperShockBeamFire';
	class'xWeapons.SuperShockRifle'.default.FireModeClass[1] = class'xWeapons.SuperShockBeamFire';

	ParseURL(URL);

	Super.ServerTraveling(URL, bItems);
}

function ParseURL(string URL)
{
	local int i;
	local string Skinz0r, Sounds, Overlay, Warmup, DD, TimedOver, TimedOverLength, GrenadesOnSpawn, EnableEnhancedNetcode;
	local array<string> Parts;

	Split(URL, "?", Parts);

	for (i = 0; i < Parts.Length; i++)
	{
		if (Parts[i] != "")
		{
			if (Left(Parts[i], Len("BrightSkinsMode")) ~= "BrightSkinsMode")
				Skinz0r = Right(Parts[i], Len(Parts[i]) - Len("BrightSkinsMode") - 1);
			if (Left(Parts[i], Len("HitSoundsMode")) ~= "HitSoundsMode")
				Sounds = Right(Parts[i], Len(Parts[i]) - Len("HitSoundsMode") - 1);
			if (Left(Parts[i], Len("EnableTeamOverlay")) ~= "EnableTeamOverlay")
				Overlay = Right(Parts[i], Len(Parts[i]) - Len("EnableTeamOverlay") - 1);
			if (Left(Parts[i], Len("EnableWarmup")) ~= "EnableWarmup")
				Warmup = Right(Parts[i], Len(Parts[i]) - Len("EnableWarmup") - 1);
			if (Left(Parts[i], Len("DoubleDamage")) ~= "DoubleDamage")
				DD = Right(Parts[i], Len(Parts[i]) - Len("DoubleDamage") - 1);
			if (Left(Parts[i], Len("EnableTimedOverTime")) ~= "EnableTimedOverTime")
				TimedOver = Right(Parts[i], Len(Parts[i]) - Len("EnableTimedOverTime") - 1);
			if (Left(Parts[i], Len("TimedOverTimeLength")) ~= "TimedOverTimeLength")
				TimedOverLength = Right(Parts[i], Len(Parts[i]) - Len("TimedOverTimeLength") - 1);
			if (Left(Parts[i], Len("GrenadesOnSpawn")) ~= "GrenadesOnSpawn")
				GrenadesOnSpawn = Right(Parts[i], Len(Parts[i]) - Len("GrenadesOnSpawn") - 1);
			if (Left(Parts[i], Len("EnableEnhancedNetcode")) ~= "EnableEnhancedNetcode")
				EnableEnhancedNetcode = Right(Parts[i], Len(Parts[i]) - Len("EnableEnhancedNetcode") - 1);
		}
	}

	if (Skinz0r != "" && int(Skinz0r) < 4 && int(Skinz0r) > 0)
	{
		default.EnableBrightskinsMode = int(Skinz0r);
		EnableBrightskinsMode = default.EnableBrightskinsMode;
	}

	if (Sounds != "" && int(Sounds) < 3 && int(Sounds) >= 0)
	{
		default.EnableHitsoundsMode = int(Sounds);
		EnableHitsoundsMode = default.EnableHitsoundsMode;
	}

	if (Overlay != "" && (Overlay ~= "false" || Overlay ~= "true"))
	{
		default.bEnableTeamOverlay = Overlay ~= "true";
		bEnableTeamOverlay = default.bEnableTeamOverlay;
	}

	if (Warmup != "" && (Warmup ~= "false" || Warmup ~= "true"))
	{
		default.bEnableWarmup = (Warmup ~= "true");
		bEnableWarmup = default.bEnableWarmup;
	}

	if (DD != "" && (DD ~= "false" || DD ~= "true"))
	{
		default.bEnableDoubleDamage = (DD ~= "true");
		bEnableDoubleDamage = default.bEnableDoubleDamage;
	}

	if (TimedOverLength != "" && int(TimedOverLength) >= 0)
	{
		if (int(TimedOverLength) == 0)
		{
			default.bEnableTimedOverTime = false;
		}
		else
		{
			default.TimedOvertimeLength = 60 * int(TimedOverLength);
			default.bEnableTimedOverTime = true;
		}
		bEnableTimedOverTime = default.bEnableTimedOverTime;
		TimedOvertimeLength = default.TimedOvertimeLength;
	}

	if (GrenadesOnSpawn != "" && int(GrenadesOnSpawn) < 9 && int(GrenadesOnSpawn) >= 0)
	{
		default.NumGrenadesOnSpawn = int(GrenadesOnSpawn);
		NumGrenadesOnSpawn = default.NumGrenadesOnSpawn;
	}

	if (EnableEnhancedNetcode != "" && (EnableEnhancedNetcode ~= "false" || EnableEnhancedNetcode ~= "true"))
	{
		default.bEnableEnhancedNetcode = (EnableEnhancedNetcode ~= "true");
		bEnhancedNetCodeEnabledAtStartOfMap = default.bEnableEnhancedNetcode;
		bEnableEnhancedNetcode = default.bEnableEnhancedNetCode;
	}

	StaticSaveConfig();
}

function AutoDemoRecord()
{
	if (class'MutBTComp'.default.bEnableAutoDemorec)
		ConsoleCommand("Demorec" @ CreateAutoDemoRecName());

	bDemoStarted = true;
}

function string CreateAutoDemoRecName()
{
	local string S;

	S = class'MutBTComp'.default.AutoDemoRecMask;
	S = Repl(S, "%p", CreatePlayerString());
	S = Repl(S, "%t", CreateTimeString());
	S = StripIllegalWindowsCharacters(S);

	return S;
}

function string CreatePlayerString()
{
	local int i;
	local string ReturnString;
	local array<string> RedPlayerNames;
	local array<string> BluePlayerNames;
	local Controller C;

	for (C = Level.ControllerList; C != none; C = C.NextController)
	{
		if (PlayerController(C) != none && C.PlayerReplicationInfo != none && !C.PlayerReplicationInfo.bOnlySpectator && C.PlayerReplicationInfo.PlayerName != "")
		{
			if (C.GetTeamNum() == 1)
				BluePlayerNames[BluePlayerNames.Length] = C.PlayerReplicationInfo.PlayerName;
			else
				RedPlayerNames[RedPlayerNames.Length] = C.PlayerReplicationInfo.PlayerName;
		}
	}

	if (BluePlayerNames.Length > 0 && RedPlayerNames.Length > 0)
	{
		ReturnString = BluePlayerNames[0];
		for (i = 1; i < BluePlayerNames.Length && i < 4; i++)
		{
			ReturnString $= "-" $ BluePlayerNames[i];
		}
		ReturnString $= "-vs-" $ RedPlayerNames[0];
		for (i = 1; i < RedPlayerNames.Length && i < 4; i++)
		{
			ReturnString $= "-" $ RedPlayerNames[i];
		}
	}
	else if (RedPlayerNames.Length > 0)
	{
		ReturnString = RedPlayerNames[0];
		for (i = 1; i < RedPlayerNames.Length && i < 8; i++)
		{
			ReturnString $= "-vs-" $ RedPlayerNames[i];
		}
	}
	else if (BluePlayerNames.Length > 0)
	{
		ReturnString = BluePlayerNames[0];
		for (i = 1; i < BluePlayerNames.Length && i < 4; i++)
		{
			ReturnString $= "-" $ BluePlayerNames[i];
		}
		ReturnString $= "-vs-EmptyTeam";
	}

	ReturnString = Left(ReturnString, 100);
	return ReturnString;
}

function GetServerDetails(out GameInfo.ServerResponseLine ServerState)
{
	Super.GetServerDetails(ServerState);

	//Level.Game.AddServerDetail(ServerState, Key, Value);
	Level.Game.AddServerDetail(ServerState, "BTComp", "Version:" @ ModVersion);
	Level.Game.AddServerDetail(ServerState, "BTComp", "Netcode:" @ string(bEnhancedNetCodeEnabledAtStartOfMap));
}

function string CreateTimeString()
{
	local string HourDigits, MinuteDigits;

	if (Len(Level.Hour) == 1)
		HourDigits = "0" $ Level.Hour;
	else
		HourDigits = Left(Level.Hour, 2);

	if (Len(Level.Minute) == 1)
		MinuteDigits = "0" $ Level.Minute;
	else
		MinuteDigits = Left(Level.Minute, 2);

	return HourDigits $ "-" $ MinuteDigits;
}

simulated function string StripIllegalWindowsCharacters(string S)
{
	S = Repl(S, ".", "-");
	S = Repl(S, "*", "-");
	S = Repl(S, ":", "-");
	S = Repl(S, "|", "-");
	S = Repl(S, "/", "-");
	S = Repl(S, ";", "-");
	S = Repl(S, "\\", "-");
	S = Repl(S, ">", "-");
	S = Repl(S, "<", "-");
	S = Repl(S, "+", "-");
	S = Repl(S, " ", "-");
	S = Repl(S, "?", "-");

	return S;
}

static function FillPlayInfo(PlayInfo PlayInfo)
{
	PlayInfo.AddClass(default.Class);
	PlayInfo.AddSetting("UTComp Settings", "EnableBrightSkinsMode", "Brightskins Mode", 1, 1, "Select", "0;Disabled;1;Epic Style;2;BrighterEpic Style;3;UTComp Style ");
	PlayInfo.AddSetting("UTComp Settings", "EnableHitSoundsMode", "Hitsounds Mode", 1, 1, "Select", "0;Disabled;1;Line Of Sight;2;Everywhere");
	PlayInfo.AddSetting("UTComp Settings", "bEnableWarmup", "Enable Warmup", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "bEnableDoubleDamage", "Enable Double Damage", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "bEnableAutoDemoRec", "Enable Serverside Demo-Recording", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "bEnableTeamOverlay", "Enable Team Overlay", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "bEnableEnhancedNetcode", "Enable Enhanced Netcode", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "ServerMaxPlayers", "Voting Max Players", 255, 1, "Text", "2;0:32",, true, true);
	PlayInfo.AddSetting("UTComp Settings", "NumGrenadesOnSpawn", "Number of grenades on spawn", 255, 1, "Text", "2;0:32",, true, true);
	PlayInfo.AddSetting("UTComp Settings", "MaxMultiDodges", "Number of additional dodges", 255, 1, "Text", "2;0:99",);
	PlayInfo.AddSetting("UTComp Settings", "MinNetSpeed", "Minimum NetSpeed for Clients", 255, 1, "Text", "0;0:100000",);
	PlayInfo.AddSetting("UTComp Settings", "MaxNetSpeed", "Maximum NetSpeed for Clients", 255, 1, "Text", "0;0:100000",);

	PlayInfo.AddSetting("UTComp Settings", "bEnableVoting", "Enable Voting", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "bEnableBrightskinsVoting", "Allow players to vote on Brightskins settings", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "bEnableWarmupVoting", "Allow players to vote on Warmup setting", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "bEnableHitsoundsVoting", "Allow players to vote on Hitsounds settings", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "bEnableTeamOverlayVoting", "Allow players to vote on team overlay setting", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "bEnableEnhancedNetcodeVoting", "Allow players to vote on enhanced netcode setting", 1, 1, "Check");
	PlayInfo.AddSetting("UTComp Settings", "WarmupTime", "Warmup Time", 1, 1, "Text", "0;0:1800",, true, true);

	PlayInfo.PopClass();
	Super.FillPlayInfo(PlayInfo);
}

static event string GetDescriptionText(string PropName)
{
	switch (PropName)
	{
		case "bEnableWarmup":
			return "Check this to enable Warmup.";
		case "bEnableDoubleDamage":
			return "Check this to enable the double damage.";
		case "EnableBrightSkinsMode":
			return "Sets the server-forced brightskins mode.";
		case "EnableHitSoundsMode":
			return "Sets the server-Forced hitsound mode.";
		case "bEnableAutoDemoRec":
			return "Check this to enable a recording of every map, beginning as warmup ends.";
		case "ServerMaxPlayers":
			return "Set this to the maximum number of players you wish for to allow a client to vote for.";
		case "NumGrenadesOnSpawn":
			return "Set this to the number of Assault Rifle grenades you wish a player to spawn with.";
		case "MaxMultiDodges":
			return "Additional dodges players can perform without landing.";
		case "bEnableTeamOverlay":
			return "Check this to enable the team overlay.";
		case "bEnableEnhancedNetcode":
			return "Check this to enable the enhanced netcode.";
		case "bEnableVoting":
			return "Check this to enable voting.";
		case "bEnableBrightSkinsVoting":
			return "Check this to enable voting for brightskins.";
		case "bEnablehitsoundsVoting":
			return "Check this to enable voting for hitsounds.";
		case "bEnableTeamOverlayVoting":
			return "Check this to enable voting for Team Overlay.";
		case "bEnableEnhancedNetcodeVoting":
			return "Check this to enable voting for Enhanced Netcode.";
		case "bEnableWarmupVoting":
			return "Check this to enable voting for Warmup.";
		case "WarmupTime":
			return "Time for warmup. Set this to 0 for unlimited, otherwise it is the time in seconds.";
		case "MinNetSpeed":
			return "Minimum NetSpeed for clients on this server";
		case "MaxNetSpeed":
			return "Maximum NetSpeed for clients on this server";
	}

	return Super.GetDescriptionText(PropName);
}

function bool ReplaceWith(Actor Other, string aClassName)
{
	local Actor A;
	local class<Actor> aClass;

	if (aClassName == "")
		return true;

	aClass = class<Actor>(DynamicLoadObject(aClassName, class'Class'));
	if (aClass != none)
		A = Spawn(aClass, Other.Owner, Other.Tag, Other.Location, Other.Rotation);

	if (Pickup(Other) != none)
	{
		if (Pickup(Other).MyMarker != none)
		{
			Pickup(Other).MyMarker.MarkedItem = Pickup(A);
			if (Pickup(A) != none)
			{
				Pickup(A).MyMarker = Pickup(Other).MyMarker;
				A.SetLocation(A.Location + (A.CollisionHeight - Other.CollisionHeight) * vect(0, 0, 1));
			}
			Pickup(Other).MyMarker = none;
		}
		else if (Pickup(A) != none && WeaponPickup(A) == none)
		{
			Pickup(A).Respawntime = 0.0;
		}
	}

	if (A != none)
	{
		A.Event = Other.Event;
		A.Tag = Other.Tag;
		return true;
	}

	return false;
}

function string GetInventoryClassOverride(string InventoryClassName)
{
	local int x;

	for (x = 0; x < WeaponType.Length; x++)
	{
		if (InventoryClassName ~= string(WeaponType[x].OldWeapon))
			return string(WeaponType[x].NetWeapon);
	}

	if (NextMutator != none)
		return NextMutator.GetInventoryClassOverride(InventoryClassName);

	return InventoryClassName;
}

defaultproperties
{
//	Voting
	bEnableVoting=true
	bEnableBrightskinsVoting=true
	bEnableHitsoundsVoting=true
	bEnableWarmupVoting=true
	bEnableTeamOverlayVoting=true
	bEnableTimedOvertimeVoting=true
	VotingPercentRequired=51.000000
	VotingTimeLimit=30.000000
	bEnableDoubleDamage=true
	EnableBrightSkinsMode=3
	bEnableClanSkins=true
	bEnableTeamOverlay=true
	EnableHitSoundsMode=1
	bEnableWarmup=true
	WarmupReadyPercentRequired=100.000000
	bShowTeamScoresInServerBrowser=true
	ServerMaxPlayers=12
	bEnableAdvancedVotingOptions=true
	AutoDemoRecMask="%d-(%t)-%m-%p"
	EnableWarmupWeaponsMode=1
	WarmupHealth=199
	TimedOverTimeLength=300
	NumGrenadesOnSpawn=4
	bShieldFix=true
	bEnableEnhancedNetCodeVoting=true
	bEnableScoreboard=true
	bEnableWeaponStats=true
	bEnablePowerupStats=true
//	Server config
	MinNetSpeed=5000
	MaxNetSpeed=100000
//	Scoreboard
	ScoreBoardType(0)=(OldSB=class'UT2k4Assault.ScoreBoard_Assault',NewSB=class'UTComp_Scoreboard_AS',EnhSB=class'UTComp_ScoreBoard')
	ScoreBoardType(1)=(OldSB=class'XInterface.ScoreBoardDeathMatch',NewSB=class'UTComp_ScoreBoard_DM',EnhSB=class'UTComp_ScoreBoard')
	ScoreBoardType(2)=(OldSB=class'XInterface.ScoreBoardTeamDeathMatch',NewSB=class'UTComp_ScoreBoard_TDM',EnhSB=class'UTComp_ScoreBoard')
	ScoreBoardType(3)=(OldSB=class'BonusPack.MutantScoreboard',NewSB=class'UTComp_Scoreboard_Mutant',EnhSB=class'UTComp_ScoreBoard')
//	Hud
	HUDType(0)=(OldHud=class'UT2k4Assault.HUD_Assault',NewHud=class'UTComp_Hud_Assault')
	HUDType(1)=(OldHud=class'XInterface.HudCDeathmatch',NewHud=class'UTComp_Hud_Deathmatch')
	HUDType(2)=(OldHud=class'XInterface.HudCTeamDeathMatch',NewHud=class'UTComp_Hud_TeamDeathmatch')
	HUDType(3)=(OldHud=class'XInterface.HudCDoubleDomination',NewHud=class'UTComp_Hud_DoubleDomination')
	HUDType(4)=(OldHud=class'XInterface.HudCCaptureTheFlag',NewHud=class'UTComp_Hud_CaptureTheFlag')
	HUDType(5)=(OldHud=class'XInterface.HudCBombingRun',NewHud=class'UTComp_Hud_BombingRun')
	HUDType(6)=(OldHud=class'BonusPack.HudLMS',NewHud=class'UTComp_Hud_LastManStanding')
	HUDType(7)=(OldHud=class'BonusPack.HudMutant',NewHud=class'UTComp_Hud_Mutant')
	HUDType(8)=(OldHud=class'Onslaught.ONSHudOnslaught',NewHud=class'UTComp_Hud_Onslaught')
//	Weapon
	WeaponType(0)=(OldWeapon=class'XWeapons.AssaultRifle',NetWeapon=class'NewNet_AssaultRifle',OldPickup=class'XWeapons.AssaultRiflePickup',NetPickup=class'NewNet_AssaultRiflePickup')
	WeaponType(1)=(OldWeapon=class'XWeapons.BioRifle',NetWeapon=class'NewNet_BioRifle',OldPickup=class'XWeapons.BioRiflePickup',NetPickup=class'NewNet_BioRiflePickup')
	WeaponType(2)=(OldWeapon=class'XWeapons.ShockRifle',NetWeapon=class'NewNet_ShockRifle',OldPickup=class'XWeapons.ShockRiflePickup',NetPickup=class'NewNet_ShockRiflePickup')
	WeaponType(3)=(OldWeapon=class'XWeapons.LinkGun',NetWeapon=class'NewNet_LinkGun',OldPickup=class'XWeapons.LinkGunPickup',NetPickup=class'NewNet_LinkGunPickup')
	WeaponType(4)=(OldWeapon=class'XWeapons.Minigun',NetWeapon=class'NewNet_Minigun',OldPickup=class'XWeapons.MinigunPickup',NetPickup=class'NewNet_MinigunPickup')
	WeaponType(5)=(OldWeapon=class'XWeapons.FlakCannon',NetWeapon=class'NewNet_FlakCannon',OldPickup=class'XWeapons.FlakCannonPickup',NetPickup=class'NewNet_FlakCannonPickup')
	WeaponType(6)=(OldWeapon=class'XWeapons.RocketLauncher',NetWeapon=class'NewNet_RocketLauncher',OldPickup=class'XWeapons.RocketLauncherPickup',NetPickup=class'NewNet_RocketLauncherPickup')
	WeaponType(7)=(OldWeapon=class'XWeapons.SniperRifle',NetWeapon=class'NewNet_LightningGun',OldPickup=class'XWeapons.SniperRiflePickup',NetPickup=class'NewNet_LightningGunPickup')
	WeaponType(8)=(OldWeapon=class'UTClassic.ClassicSniperRifle',NetWeapon=class'NewNet_ClassicSniperRifle',OldPickup=class'UTClassic.ClassicSniperRiflePickup',NetPickup=class'NewNet_ClassicSniperRiflePickup')
	WeaponType(9)=(OldWeapon=class'Onslaught.ONSMineLayer',NetWeapon=class'NewNet_ONSMineLayer',OldPickup=class'Onslaught.ONSMineLayerPickup',NetPickup=class'NewNet_ONSMineLayerPickup')
	WeaponType(10)=(OldWeapon=class'Onslaught.ONSGrenadeLauncher',NetWeapon=class'NewNet_ONSGrenadeLauncher',OldPickup=class'Onslaught.ONSGrenadePickup',NetPickup=class'NewNet_ONSGrenadePickup')
	WeaponType(11)=(OldWeapon=class'Onslaught.ONSAVRiL',NetWeapon=class'NewNet_ONSAvril',OldPickup=class'Onslaught.ONSAVRiLPickup',NetPickup=class'NewNet_ONSAvrilPickup')
	WeaponType(12)=(OldWeapon=class'XWeapons.SuperShockRifle',NetWeapon=class'NewNet_SuperShockRifle')
//	Mutator
	bAddToServerPackages=true
	FriendlyName="BTComp 3.5"
	Description="A mutator for warmup, brightskins, hitsounds, and various other features.|discord: voltz1"
	bNetTemporary=true
	bAlwaysRelevant=true
	RemoteRole=ROLE_SimulatedProxy
}