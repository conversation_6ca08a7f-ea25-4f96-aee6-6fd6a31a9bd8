class UTComp_HudSettings extends Object;

struct SpecialCrosshair
{
	var Texture CrossTex;
	var float CrossScale;
	var Color CrossColor;
	var float OffsetX;
	var float OffsetY;
};
var config array<SpecialCrosshair> UTCompCrosshairs;
var SpecialCrosshair TempxHair;

var config bool bEnableUTCompCrosshairs;
var config bool bEnableCrosshairSizing;
var config bool bMatchHudColor;

defaultproperties
{
	bEnableCrosshairSizing=true
}