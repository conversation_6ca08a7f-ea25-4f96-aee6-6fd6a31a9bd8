/*
	UTComp_HudCommon.uci - Common HUD functions for all UTComp HUD classes
	Include this file in UTComp HUD classes to eliminate code duplication
*/

const PICKUP_SCALE_DURATION = 0.4;
const PICKUP_SCALE_PEAK_TIME = 0.2;
const PICKUP_SCALE_MULTIPLIER = 5.0;
const CROSSHAIR_TEXTURE_SIZE = 64;
const CROSSHAIR_SCALE_FACTOR = 0.5;
const BASE_RESOLUTION_HEIGHT = 1080.0;

var UTComp_HudSettings HudConfig;

simulated event PostBeginPlay()
{
	Super.PostBeginPlay();

	foreach AllObjects(class'UTComp_HudSettings', HudConfig)
		break;

	if (HudConfig == none)
		Warn(self @ "HudConfig object not found!");
}

simulated function UpdatePrecacheMaterials()
{
	local int i;

	if (HudConfig.bEnableUTCompCrosshairs)
	{
		for (i = 0; i < HudConfig.UTCompCrosshairs.Length; i++)
		{
			if (HudConfig.UTCompCrosshairs[i].CrossTex != none)
				Level.AddPrecacheMaterial(HudConfig.UTCompCrosshairs[i].CrossTex);
		}
	}

	Super.UpdatePrecacheMaterials();
}

exec function NextStats()
{
	if (ScoreBoard == none || !bShowScoreBoard)
		Super.NextStats();
	else
		ScoreBoard.NextStats();
}

function DisplayEnemyName(Canvas C, PlayerReplicationInfo PRI)
{
	PlayerOwner.ReceiveLocalizedMessage(class'BTM_PlayerName', 0, PRI);
}

simulated function float GetPickupScaleMultiplier()
{
	local float TimeDiff;

	if (!HudConfig.bEnableCrosshairSizing || LastPickupTime <= Level.TimeSeconds - PICKUP_SCALE_DURATION)
		return 1.0;

	TimeDiff = Level.TimeSeconds - LastPickupTime;

	if (TimeDiff < PICKUP_SCALE_PEAK_TIME)
		return 1.0 + PICKUP_SCALE_MULTIPLIER * TimeDiff;
	else
		return 1.0 + PICKUP_SCALE_MULTIPLIER * (LastPickupTime + PICKUP_SCALE_DURATION - Level.TimeSeconds);
}

simulated function DrawTimer(Canvas C)
{
	local int Seconds, Minutes, Hours;
	local GameReplicationInfo GRI;
	local BTRI_Warmup uWarmup;
	local float UniformScale;

	GRI = PlayerOwner.GameReplicationInfo;
	if (BS_xPlayer(PlayerOwner) != none)
	{
		if (BS_xPlayer(PlayerOwner).uWarmup != none)
			uWarmup = BS_xPlayer(PlayerOwner).uWarmup;
	}

	if (GRI.TimeLimit == 0)
		Seconds = GRI.ElapsedTime;
	else if (GRI.RemainingTime > 0 || GRI.ElapsedTime < 60 || (uWarmup != none && uWarmup.bInWarmup))
		Seconds = GRI.RemainingTime;
	else
		Seconds = GRI.ElapsedTime - GRI.TimeLimit * 60 - 1;

	UniformScale = C.SizeY / BASE_RESOLUTION_HEIGHT;

	TimerBackground.Tints[TeamIndex] = HudColorBlack;
	TimerBackground.Tints[TeamIndex].A = 150;

	DrawSpriteWidget(C, TimerBackground);
	DrawSpriteWidget(C, TimerBackgroundDisc);
	DrawSpriteWidget(C, TimerIcon);

	TimerMinutes.OffsetX = (default.TimerMinutes.OffsetX - 80) * UniformScale;
	TimerSeconds.OffsetX = (default.TimerSeconds.OffsetX - 80) * UniformScale;
	TimerDigitSpacer[0].OffsetX = default.TimerDigitSpacer[0].OffsetX * UniformScale;
	TimerDigitSpacer[1].OffsetX = default.TimerDigitSpacer[1].OffsetX * UniformScale;

	if (Seconds > 3600)
	{
		Hours = Seconds / 3600;
		Seconds -= Hours * 3600;

		DrawNumericWidget(C, TimerHours, DigitsBig);
		TimerHours.Value = Hours;

		if (Hours > 9)
		{
			TimerMinutes.OffsetX = default.TimerMinutes.OffsetX * UniformScale;
			TimerSeconds.OffsetX = default.TimerSeconds.OffsetX * UniformScale;
		}
		else
		{
			TimerMinutes.OffsetX = (default.TimerMinutes.OffsetX - 40) * UniformScale;
			TimerSeconds.OffsetX = (default.TimerSeconds.OffsetX - 40) * UniformScale;
			TimerDigitSpacer[0].OffsetX = (default.TimerDigitSpacer[0].OffsetX - 32) * UniformScale;
			TimerDigitSpacer[1].OffsetX = (default.TimerDigitSpacer[1].OffsetX - 32) * UniformScale;
		}

		DrawSpriteWidget(C, TimerDigitSpacer[0]);
	}

	DrawSpriteWidget(C, TimerDigitSpacer[1]);

	Minutes = Seconds / 60;
	Seconds -= Minutes * 60;

	TimerMinutes.Value = Clamp(Minutes, 0, 60);
	TimerSeconds.Value = Clamp(Seconds, 0, 60);

	DrawNumericWidget(C, TimerMinutes, DigitsBig);
	DrawNumericWidget(C, TimerSeconds, DigitsBig);
}

//	#Custom Crosshairs + Rescaling
simulated function DrawCrosshair(Canvas C)
{
	if (HudConfig.bEnableUTCompCrosshairs && HudConfig.UTCompCrosshairs.Length > 0)
		RenderCrosshairNew(C);
	else
		RenderCrosshairOld(C);
}

simulated function RenderCrosshairNew(Canvas C)
{
	local int i;
	local float OldScale, UniformScale, ScaleMultiplier;
	local array<SpriteWidget> CHTexture;
	local Plane OldModulate;
	local Color OldColor;

	if (PawnOwner.bSpecialCrosshair)
	{
		PawnOwner.SpecialDrawCrosshair(C);
		return;
	}

	if (!bCrosshairShow)
		return;

	if (HudConfig.UTCompCrosshairs.Length <= 0)
		return;

	UniformScale = C.SizeY / BASE_RESOLUTION_HEIGHT;
	ScaleMultiplier = GetPickupScaleMultiplier();
	CHTexture.Length = HudConfig.UTCompCrosshairs.Length;

	for (i = 0; i < HudConfig.UTCompCrosshairs.Length; i++)
	{
		if (HudConfig.UTCompCrosshairs[i].CrossTex == none)
		{
			Log("Warning: UTComp crosshair texture" @ i @ "is none!");
			continue;
		}

		CHTexture[i].WidgetTexture = HudConfig.UTCompCrosshairs[i].CrossTex;
		CHTexture[i].RenderStyle = STY_Alpha;
		CHTexture[i].TextureCoords.X2 = CROSSHAIR_TEXTURE_SIZE;
		CHTexture[i].TextureCoords.Y2 = CROSSHAIR_TEXTURE_SIZE;
		CHTexture[i].TextureScale = HudConfig.UTCompCrosshairs[i].CrossScale * CROSSHAIR_SCALE_FACTOR * UniformScale * ScaleMultiplier;
		CHTexture[i].DrawPivot = DP_MiddleMiddle;
		CHTexture[i].PosX = HudConfig.UTCompCrosshairs[i].OffsetX * UniformScale;
		CHTexture[i].PosY = HudConfig.UTCompCrosshairs[i].OffsetY * UniformScale;
		CHTexture[i].ScaleMode = SM_None;
		CHTexture[i].Scale = 1.0;
		CHTexture[i].Tints[0] = HudConfig.UTCompCrosshairs[i].CrossColor;
		CHTexture[i].Tints[1] = HudConfig.UTCompCrosshairs[i].CrossColor;
	}

	OldModulate = C.ColorModulate;
	OldColor = C.DrawColor;
	OldScale = HudScale;

	C.ColorModulate.X = 1.0;
	C.ColorModulate.Y = 1.0;
	C.ColorModulate.Z = 1.0;
	C.ColorModulate.W = 1.0;
	C.Style = ERenderStyle.STY_Alpha;
	HudScale = 1.0;

	for (i = 0; i < CHTexture.Length; i++)
	{
		if (CHTexture[i].WidgetTexture != none)
			DrawSpriteWidget(C, CHTexture[i]);
	}

	C.ColorModulate = OldModulate;
	C.DrawColor = OldColor;
	HudScale = OldScale;

	DrawEnemyName(C);
}

//	#Original HudCDeathmatch::DrawCrosshair + Pickup Sizing
simulated function RenderCrosshairOld(Canvas C)
{
	local int i, CurrentCrosshair;
	local float NormalScale, OldScale, OldW, CurrentCrosshairScale;
	local float UniformScale, ScaleMultiplier;
	local Color CurrentCrosshairColor;
	local SpriteWidget CHTexture;

	if (PawnOwner.bSpecialCrosshair)
	{
		PawnOwner.SpecialDrawCrosshair(C);
		return;
	}

	if (!bCrosshairShow)
		return;

	if (bUseCustomWeaponCrosshairs && (PawnOwner != none) && (PawnOwner.Weapon != none))
	{
		CurrentCrosshair = PawnOwner.Weapon.CustomCrosshair;
		if (CurrentCrosshair == -1 || CurrentCrosshair == Crosshairs.Length)
		{
			CurrentCrosshair = CrosshairStyle;
			CurrentCrosshairColor = CrosshairColor;
			CurrentCrosshairScale = CrosshairScale;
		}
		else
		{
			CurrentCrosshairColor = PawnOwner.Weapon.CustomCrosshairColor;
			CurrentCrosshairScale = PawnOwner.Weapon.CustomCrosshairScale;
			if (PawnOwner.Weapon.CustomCrosshairTextureName != "")
			{
				if (PawnOwner.Weapon.CustomCrosshairTexture == none)
				{
					PawnOwner.Weapon.CustomCrosshairTexture = Texture(DynamicLoadObject(PawnOwner.Weapon.CustomCrosshairTextureName, class'Texture'));
					if (PawnOwner.Weapon.CustomCrosshairTexture == none)
					{
						Log(PawnOwner.Weapon @ "custom crosshair texture not found!");
						PawnOwner.Weapon.CustomCrosshairTextureName = "";
					}
				}

				CHTexture = Crosshairs[0];
				CHTexture.WidgetTexture = PawnOwner.Weapon.CustomCrosshairTexture;
			}
		}
	}
	else
	{
		CurrentCrosshair = CrosshairStyle;
		CurrentCrosshairColor = CrosshairColor;
		CurrentCrosshairScale = CrosshairScale;
	}

	CurrentCrosshair = Clamp(CurrentCrosshair, 0, Crosshairs.Length - 1);
	NormalScale = Crosshairs[CurrentCrosshair].TextureScale;

	if (CHTexture.WidgetTexture == none)
		CHTexture = Crosshairs[CurrentCrosshair];

	UniformScale = C.SizeY / BASE_RESOLUTION_HEIGHT;
	ScaleMultiplier = GetPickupScaleMultiplier();

	CHTexture.TextureScale *= CROSSHAIR_SCALE_FACTOR * CurrentCrosshairScale * UniformScale * ScaleMultiplier;

	for (i = 0; i < ArrayCount(CHTexture.Tints); i++)
		CHTexture.Tints[i] = CurrentCrosshairColor;

	OldScale = HudScale;
	HudScale = 1.0;
	OldW = C.ColorModulate.W;
	C.ColorModulate.W = 1.0;
	DrawSpriteWidget(C, CHTexture);

	C.ColorModulate.W = OldW;
	HudScale = OldScale;
	CHTexture.TextureScale = NormalScale;

	DrawEnemyName(C);
}

//	#Spectating WeaponBar
simulated function DrawSpectatingHud(Canvas C)
{
	Super.DrawSpectatingHud(C);

	if (PawnOwner != none && PawnOwner.Health > 0)
	{
		UpdateHud();

		if (bShowWeaponBar && PawnOwner.Weapon != none)
			DrawWeaponBar(C);

		if (bShowWeaponInfo && PawnOwner.Weapon != none)
		{
			if (PawnOwner.Weapon.bShowChargingBar)
				DrawChargeBar(C);

			DrawHudPassA(C);
		}

		if (bShowPersonalInfo)
			DrawHudPassA(C);
	}
}